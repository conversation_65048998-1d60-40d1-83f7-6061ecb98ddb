// CSV field mapping types and interfaces for energy certificate export

export interface CsvFieldMapping {
  csvColumn: string;
  dbPath: string; // Path to the field in the database (e.g., 'objektdaten.PLZ' or 'gebaeudedetails1.Baujahr')
  certificateTypes: ('WG/V' | 'WG/B' | 'NWG/V')[]; // Which certificate types this field applies to
  defaultValue?: string | number;
  transform?: (value: any) => string; // Optional transformation function
}

export interface EnergieausweisData {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  objektdaten: Record<string, any> | null;
  gebaeudedetails1: Record<string, any> | null;
  gebaeudedetails2: Record<string, any> | null;
  fenster: Record<string, any> | null;
  heizung: Record<string, any> | null;
  trinkwarmwasser: Record<string, any> | null;
  lueftung: Record<string, any> | null;
  verbrauchsdaten: Record<string, any> | null;
  payment_status: string | null;
  certificate_type: string | null;
  stripe_checkout_session_id: string | null;
  order_number: string | null;
}

// Enum value mappings for CSV export
export const csvEnumMappings: Record<string, Record<string, string>> = {
  BedarfVerbrauch: {
    'V': 'V',
    'B': 'B'
  },
  Anlass: {
    'AG_VERMIETUNG': 'AG_VERMIETUNG',
    'AG_AUSHANG': 'AG_AUSHANG', 
    'AG_SONST': 'AG_SONST'
  },
  Datenerhebung: {
    '0': '0',
    '1': '1',
    '2': '2'
  },
  nichtWohnGeb: {
    '0': '0',
    '1': '1'
  },
  isGebaeudehuelle: {
    '0': '0',
    '1': '1'
  },
  anbauSituation: {
    '0': '0',
    '1': '1',
    '2': '2'
  },
  Keller_beheizt: {
    '0': '0',
    '1': '1'
  },
  Klimatisiert: {
    '0': '0',
    '1': '1'
  },
  WSchVo77_erfuellt: {
    '0': '0',
    '1': '1'
  },
  Originaldaemmstandard: {
    '0': '0',
    '1': '1',
    '2': '2'
  },
  Fensterlüftung: {
    '0': '0',
    '1': '1'
  },
  Schachtlüftung: {
    '0': '0',
    '1': '1'
  },
  L_Mit_WRG: {
    '0': '0',
    '1': '1'
  },
  L_Ohne_WRG: {
    '0': '0',
    '1': '1'
  },
  // Add more enum mappings as needed
  ETr1_Heizung: {
    '0': '0',
    '1': '1'
  },
  ETr1_TWW: {
    '0': '0',
    '1': '1'
  },
  ETr1_isFw: {
    '0': '0',
    '1': '1'
  },
  ETr1_gebaeudeNahErzeugt: {
    '0': '0',
    '1': '1'
  }
};
