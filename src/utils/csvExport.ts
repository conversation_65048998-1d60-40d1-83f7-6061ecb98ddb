import { type CsvFieldMapping, type EnergieausweisData, csvEnumMappings } from '../types/csvMapping';

// CSV field mappings based on Schnittstellenbeschreibung.md
export const csvFieldMappings: CsvFieldMapping[] = [
  // BOM field (empty for first column)
  { csvColumn: 'BOM', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '' },
  
  // Basic object information
  { csvColumn: 'ID', dbPath: 'order_number', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Anlass', dbPath: 'gebaeudedetails1.Anlass', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: 'AG_VERMIETUNG' },
  { csvColumn: 'PLZ', dbPath: 'objektdaten.PLZ', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Ort', dbPath: 'objektdaten.Ort', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Straße', dbPath: 'objektdaten.Strasse', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Hausnr', dbPath: 'objektdaten.Hausnr', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },

  // Customer data fields
  { csvColumn: 'Kunden_Anrede', dbPath: 'objektdaten.Kunden_Anrede', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_Vorname', dbPath: 'objektdaten.Kunden_Vorname', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_Nachname', dbPath: 'objektdaten.Kunden_Nachname', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_Straße', dbPath: 'objektdaten.Kunden_Strasse', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_Hausnr', dbPath: 'objektdaten.Kunden_Hausnr', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_PLZ', dbPath: 'objektdaten.Kunden_PLZ', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_Ort', dbPath: 'objektdaten.Kunden_Ort', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_email', dbPath: 'objektdaten.Kunden_email', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Kunden_telefon', dbPath: 'objektdaten.Kunden_telefon', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },

  // DIBt registration number (placeholder)
  { csvColumn: 'DIBt-Registriernummer', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '' },

  // Building part auto (placeholder)
  { csvColumn: 'gebaeudeteilAuto', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: 'false' },
  
  // Building details
  { csvColumn: 'isGebaeudehuelle', dbPath: 'gebaeudedetails1.isGebaeudehuelle', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '1' },
  { csvColumn: 'Baujahr', dbPath: 'gebaeudedetails1.Baujahr', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'Wohneinheiten', dbPath: 'gebaeudedetails1.Wohneinheiten', certificateTypes: ['WG/V', 'WG/B'] },
  { csvColumn: 'Wohnfläche', dbPath: 'gebaeudedetails1.Wohnfläche', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  { csvColumn: 'gebaeudeteil', dbPath: 'objektdaten.gebaeudeteil', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  
  // Certificate type specific fields
  { csvColumn: 'Keller_beheizt', dbPath: 'gebaeudedetails1.Keller_beheizt', certificateTypes: ['WG/V'], defaultValue: '0' },
  { csvColumn: 'Raumhöhe', dbPath: 'gebaeudedetails1.Raumhöhe', certificateTypes: ['WG/B'], defaultValue: '2.5' },
  { csvColumn: 'Volumen', dbPath: 'gebaeudedetails1.Volumen', certificateTypes: ['WG/B'] },
  { csvColumn: 'Geschosse', dbPath: 'gebaeudedetails1.Geschosse', certificateTypes: ['WG/B'] },
  { csvColumn: 'anbauSituation', dbPath: 'gebaeudedetails1.anbauSituation', certificateTypes: ['WG/B'], defaultValue: '0' },
  { csvColumn: 'Nutzung1_ID', dbPath: 'gebaeudedetails1.Nutzung1_ID', certificateTypes: ['NWG/V'] },
  { csvColumn: 'Nutzung1_Flaeche', dbPath: 'gebaeudedetails1.Nutzung1_Flaeche', certificateTypes: ['NWG/V'] },
  
  // Images (placeholder fields)
  { csvColumn: 'bilderStreams_0', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '' },
  { csvColumn: 'bilderStreams_1', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '' },
  { csvColumn: 'bilderStreams_2', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '' },
  
  // Climate and cooling
  { csvColumn: 'Klimatisiert', dbPath: 'gebaeudedetails1.Klimatisiert', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '1' },
  { csvColumn: 'kuehlWfl', dbPath: 'gebaeudedetails1.kuehlWfl', certificateTypes: ['WG/V', 'WG/B'], defaultValue: '0' },
  { csvColumn: 'passiveKuehlung', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'fernKuehlung', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'stromKuehlung', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '1' },
  { csvColumn: 'waermeKuehlung', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '1' },
  
  // Inspection data
  { csvColumn: 'kaiAnzahl', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'kaiDatum', dbPath: '', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '00.01.1900' },
  
  // Heating system
  { csvColumn: 'baujahrHzErz', dbPath: 'gebaeudedetails2.Hzg_Baujahr', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  
  // Solar and heat pump systems
  { csvColumn: 'TW_Solar', dbPath: 'trinkwarmwasser.TW_Solar', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'HZ_Solar', dbPath: 'verbrauchsdaten.HZ_Solar', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'TW_WP', dbPath: 'verbrauchsdaten.TW_WP', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'HZ_WP', dbPath: 'verbrauchsdaten.HZ_WP', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  
  // Ventilation
  { csvColumn: 'Fensterlüftung', dbPath: 'gebaeudedetails2.Fensterlüftung', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'Schachtlüftung', dbPath: 'gebaeudedetails2.Schachtlüftung', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'L_Mit_WRG', dbPath: 'gebaeudedetails2.L_Mit_WRG', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'L_Ohne_WRG', dbPath: 'gebaeudedetails2.L_Ohne_WRG', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  
  // Modernization
  { csvColumn: 'Modernisierung', dbPath: 'gebaeudedetails1.Modernisierung', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },
  
  // Insulation (Dämmung) - using component data extraction for WG/B, direct paths for others
  { csvColumn: 'Dach1_Dämmung', dbPath: 'Dach1_Dämmung', certificateTypes: ['WG/B'], defaultValue: '18' },
  { csvColumn: 'Wand1_Dämmung', dbPath: 'Wand1_Dämmung', certificateTypes: ['WG/B'] },
  { csvColumn: 'Boden1_Dämmung', dbPath: 'Boden1_Dämmung', certificateTypes: ['WG/B'], defaultValue: '0' },
  
  // Window replacement
  { csvColumn: 'bjFensterAustausch', dbPath: 'gebaeudedetails2.bjFensterAustausch', certificateTypes: ['WG/V'] },
  
  // Energy consumption data (ETr1) - for WG/V and NWG/V
  { csvColumn: 'ETr1_Kategorie', dbPath: 'verbrauchsdaten.ETr1_Kategorie', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Anteil_erneuerbar', dbPath: 'verbrauchsdaten.ETr1_Anteil_erneuerbar', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_Anteil_KWK', dbPath: 'verbrauchsdaten.ETr1_Anteil_KWK', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_isFw', dbPath: 'verbrauchsdaten.ETr1_isFw', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_gebaeudeNahErzeugt', dbPath: 'verbrauchsdaten.ETr1_gebaeudeNahErzeugt', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },

  // ETr1 consumption periods
  { csvColumn: 'ETr1_Jahr1_von', dbPath: 'verbrauchsdaten.ETr1_Jahr1_von', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr1_bis', dbPath: 'verbrauchsdaten.ETr1_Jahr1_bis', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr1_Menge', dbPath: 'verbrauchsdaten.ETr1_Jahr1_Menge', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_TWW', dbPath: 'verbrauchsdaten.ETr1_TWW', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '1' },
  { csvColumn: 'ETr1_Jahr1_Menge_TWW', dbPath: 'verbrauchsdaten.ETr1_Jahr1_Menge_TWW', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '' },
  { csvColumn: 'ETr1_Jahr1_Leerstand', dbPath: 'verbrauchsdaten.ETr1_Jahr1_Leerstand', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },

  { csvColumn: 'ETr1_Jahr2_von', dbPath: 'verbrauchsdaten.ETr1_Jahr2_von', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr2_bis', dbPath: 'verbrauchsdaten.ETr1_Jahr2_bis', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr2_Menge', dbPath: 'verbrauchsdaten.ETr1_Jahr2_Menge', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr2_Menge_TWW', dbPath: 'verbrauchsdaten.ETr1_Jahr2_Menge_TWW', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '' },
  { csvColumn: 'ETr1_Jahr2_Leerstand', dbPath: 'verbrauchsdaten.ETr1_Jahr2_Leerstand', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },

  { csvColumn: 'ETr1_Jahr3_von', dbPath: 'verbrauchsdaten.ETr1_Jahr3_von', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr3_bis', dbPath: 'verbrauchsdaten.ETr1_Jahr3_bis', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr3_Menge', dbPath: 'verbrauchsdaten.ETr1_Jahr3_Menge', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr1_Jahr3_Menge_TWW', dbPath: 'verbrauchsdaten.ETr1_Jahr3_Menge_TWW', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '' },
  { csvColumn: 'ETr1_Jahr3_Leerstand', dbPath: 'verbrauchsdaten.ETr1_Jahr3_Leerstand', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_PrimFaktor', dbPath: 'verbrauchsdaten.ETr1_PrimFaktor', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '1' },

  // ETr2 fields (similar structure)
  { csvColumn: 'ETr2_Kategorie', dbPath: 'verbrauchsdaten.ETr2_Kategorie', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr1_von', dbPath: 'verbrauchsdaten.ETr2_Jahr1_von', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr1_bis', dbPath: 'verbrauchsdaten.ETr2_Jahr1_bis', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr1_Menge', dbPath: 'verbrauchsdaten.ETr2_Jahr1_Menge', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_TWW', dbPath: 'verbrauchsdaten.ETr2_TWW', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '1' },
  { csvColumn: 'ETr2_Jahr1_Menge_TWW', dbPath: 'verbrauchsdaten.ETr2_Jahr1_Menge_TWW', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr1_Leerstand', dbPath: 'verbrauchsdaten.ETr2_Jahr1_Leerstand', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr2_von', dbPath: 'verbrauchsdaten.ETr2_Jahr2_von', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr2_bis', dbPath: 'verbrauchsdaten.ETr2_Jahr2_bis', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr2_Menge', dbPath: 'verbrauchsdaten.ETr2_Jahr2_Menge', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr2_Menge_TWW', dbPath: 'verbrauchsdaten.ETr2_Jahr2_Menge_TWW', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr2_Leerstand', dbPath: 'verbrauchsdaten.ETr2_Jahr2_Leerstand', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr3_von', dbPath: 'verbrauchsdaten.ETr2_Jahr3_von', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr3_bis', dbPath: 'verbrauchsdaten.ETr2_Jahr3_bis', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr3_Menge', dbPath: 'verbrauchsdaten.ETr2_Jahr3_Menge', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr3_Menge_TWW', dbPath: 'verbrauchsdaten.ETr2_Jahr3_Menge_TWW', certificateTypes: ['WG/V', 'NWG/V'] },
  { csvColumn: 'ETr2_Jahr3_Leerstand', dbPath: 'verbrauchsdaten.ETr2_Jahr3_Leerstand', certificateTypes: ['WG/V', 'NWG/V'] },

  // Usage flags for ETr1
  { csvColumn: 'ETr1_Heizung', dbPath: 'verbrauchsdaten.ETr1_Heizung', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '1' },
  { csvColumn: 'ETr2_Heizung', dbPath: 'verbrauchsdaten.ETr2_Heizung', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '1' },
  { csvColumn: 'ETr2_PrimFaktor', dbPath: 'verbrauchsdaten.ETr2_PrimFaktor', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '1' },

  // NWG/V specific fields
  { csvColumn: 'ETr1_ZusatzHz', dbPath: 'verbrauchsdaten.ETr1_ZusatzHz', certificateTypes: ['NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_Lueften', dbPath: 'verbrauchsdaten.ETr1_Lueften', certificateTypes: ['NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_Licht', dbPath: 'verbrauchsdaten.ETr1_Licht', certificateTypes: ['NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_Kuehlen', dbPath: 'verbrauchsdaten.ETr1_Kuehlen', certificateTypes: ['NWG/V'], defaultValue: '0' },
  { csvColumn: 'ETr1_Sonst', dbPath: 'verbrauchsdaten.ETr1_Sonst', certificateTypes: ['NWG/V'], defaultValue: '0' },

  // Certificate type and data collection
  { csvColumn: 'Gebäudetyp', dbPath: 'gebaeudedetails1.nichtWohnGeb', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '0' },
  { csvColumn: 'Datenerhebung', dbPath: 'gebaeudedetails1.Datenerhebung', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'], defaultValue: '2' },
  { csvColumn: 'BedarfVerbrauch', dbPath: 'gebaeudedetails1.BedarfVerbrauch', certificateTypes: ['WG/V', 'WG/B', 'NWG/V'] },

  // ETr2 additional fields
  { csvColumn: 'ETr2_Anteil_KWK', dbPath: 'verbrauchsdaten.ETr2_Anteil_KWK', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '#NV' },
  { csvColumn: 'ETr2_Anteil_erneuerbar', dbPath: 'verbrauchsdaten.ETr2_Anteil_erneuerbar', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '#NV' },
  { csvColumn: 'ETr2_isFw', dbPath: 'verbrauchsdaten.ETr2_isFw', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '#NV' },
  { csvColumn: 'ETr2_gebaeudeNahErzeugt', dbPath: 'verbrauchsdaten.ETr2_gebaeudeNahErzeugt', certificateTypes: ['WG/V', 'NWG/V'], defaultValue: '#NV' },

  // WG/V specific field
  { csvColumn: 'WSchVo77_erfuellt', dbPath: 'gebaeudedetails1.WSchVo77_erfuellt', certificateTypes: ['WG/V'], defaultValue: '1' },

  // WG/B specific building component fields
  // Boden (Floor) components - using component data extraction
  { csvColumn: 'Boden1', dbPath: 'Boden1', certificateTypes: ['WG/B'] },
  { csvColumn: 'Boden1_massiv', dbPath: 'Boden1_massiv', certificateTypes: ['WG/B'] },
  { csvColumn: 'Boden1_Kellerdecke', dbPath: 'Boden1_Kellerdecke', certificateTypes: ['WG/B'], defaultValue: '1' },
  { csvColumn: 'Boden1_Fläche', dbPath: 'Boden1_Fläche', certificateTypes: ['WG/B'] },
  { csvColumn: 'Boden1_U-Wert', dbPath: 'Boden1_U-Wert', certificateTypes: ['WG/B'] },

  // Dach (Roof) components - using component data extraction
  { csvColumn: 'Dach1', dbPath: 'Dach1', certificateTypes: ['WG/B'] },
  { csvColumn: 'Dach1_massiv', dbPath: 'Dach1_massiv', certificateTypes: ['WG/B'] },
  { csvColumn: 'Dach1_Geschossdecke', dbPath: 'Dach1_Geschossdecke', certificateTypes: ['WG/B'], defaultValue: '0' },
  { csvColumn: 'Dach1_Fläche', dbPath: 'Dach1_Fläche', certificateTypes: ['WG/B'] },
  { csvColumn: 'Dach1_U-Wert', dbPath: 'Dach1_U-Wert', certificateTypes: ['WG/B'] },

  // Wand (Wall) components - using component data extraction
  { csvColumn: 'Wand1', dbPath: 'Wand1', certificateTypes: ['WG/B'] },
  { csvColumn: 'Wand1_massiv', dbPath: 'Wand1_massiv', certificateTypes: ['WG/B'] },
  { csvColumn: 'Wand1_Fläche', dbPath: 'Wand1_Fläche', certificateTypes: ['WG/B'] },
  { csvColumn: 'Wand1_U-Wert', dbPath: 'Wand1_U-Wert', certificateTypes: ['WG/B'] },

  // Fenster (Window) components - using component data extraction
  { csvColumn: 'Fenster1', dbPath: 'Fenster1', certificateTypes: ['WG/B'] },
  { csvColumn: 'Fenster1_Art', dbPath: 'Fenster1_Art', certificateTypes: ['WG/B'] },
  { csvColumn: 'Fenster1_Fläche', dbPath: 'Fenster1_Fläche', certificateTypes: ['WG/B'] },
  { csvColumn: 'Fenster1_U-Wert', dbPath: 'Fenster1_U-Wert', certificateTypes: ['WG/B'] },
  { csvColumn: 'Fenster1_Ausrichtung', dbPath: 'Fenster1_Ausrichtung', certificateTypes: ['WG/B'] },
  { csvColumn: 'Fenster1_Baujahr', dbPath: 'Fenster1_Baujahr', certificateTypes: ['WG/B'] },

  // Heating system details for WG/B
  { csvColumn: 'Hzg_Speicher_Baujahr', dbPath: 'heizung.Hzg_Speicher_Baujahr', certificateTypes: ['WG/B'] },
  { csvColumn: 'Hzg_Verteilung_Baujahr', dbPath: 'heizung.Hzg_Verteilung_Baujahr', certificateTypes: ['WG/B'] },
  { csvColumn: 'Hzg_Übergabe', dbPath: 'heizung.Hzg_Übergabe', certificateTypes: ['WG/B'], defaultValue: '0' },
  { csvColumn: 'Hzg_Verteilung_Art', dbPath: 'heizung.Hzg_Verteilung_Art', certificateTypes: ['WG/B'], defaultValue: '2' },
  { csvColumn: 'Hzg_kreistemperatur', dbPath: 'heizung.Hzg_kreistemperatur', certificateTypes: ['WG/B'], defaultValue: '1' },
  { csvColumn: 'Hzg_Verteilung_Dämmung', dbPath: 'heizung.Hzg_Verteilung_Dämmung', certificateTypes: ['WG/B'], defaultValue: '1' },
  { csvColumn: 'Hzg_Speicher', dbPath: 'heizung.Hzg_Speicher', certificateTypes: ['WG/B'], defaultValue: '1' },
  { csvColumn: 'Hzg_Aufstellung', dbPath: 'heizung.Hzg_Aufstellung', certificateTypes: ['WG/B'] },
  { csvColumn: 'Hzg_Technik', dbPath: 'heizung.Hzg_Technik', certificateTypes: ['WG/B'] },
  { csvColumn: 'Hzg_Energieträger', dbPath: 'heizung.Hzg_Energieträger', certificateTypes: ['WG/B'] },
  { csvColumn: 'Hzg_PrimFaktor', dbPath: 'heizung.Hzg_PrimFaktor', certificateTypes: ['WG/B'], defaultValue: '1' },

  // Drinking water system details for WG/B
  { csvColumn: 'TW_Baujahr', dbPath: 'trinkwarmwasser.TW_Baujahr', certificateTypes: ['WG/B'] },
  { csvColumn: 'TW_Speicher_Baujahr', dbPath: 'trinkwarmwasser.TW_Speicher_Baujahr', certificateTypes: ['WG/B'] },
  { csvColumn: 'TW_Verteilung_Baujahr', dbPath: 'trinkwarmwasser.TW_Verteilung_Baujahr', certificateTypes: ['WG/B'] },
  { csvColumn: 'TW_Verteilung_Art', dbPath: 'trinkwarmwasser.TW_Verteilung_Art', certificateTypes: ['WG/B'], defaultValue: '1' },
  { csvColumn: 'TW_Verteilung_Dämmung', dbPath: 'trinkwarmwasser.TW_Verteilung_Dämmung', certificateTypes: ['WG/B'], defaultValue: '1' },
  { csvColumn: 'TW_Zirkulation', dbPath: 'trinkwarmwasser.TW_Zirkulation', certificateTypes: ['WG/B'], defaultValue: '1' },
  { csvColumn: 'TW_Speicher_Standort', dbPath: 'trinkwarmwasser.TW_Speicher_Standort', certificateTypes: ['WG/B'], defaultValue: '2' },
  { csvColumn: 'TW_Technik', dbPath: 'trinkwarmwasser.TW_Technik', certificateTypes: ['WG/B'] },

  // Ventilation system details for WG/B
  { csvColumn: 'Luft_Baujahr', dbPath: 'lueftung.Luft_Baujahr', certificateTypes: ['WG/B'] },
  { csvColumn: 'Luft_Verteilung_Baujahr', dbPath: 'lueftung.Luft_Verteilung_Baujahr', certificateTypes: ['WG/B'] },
  { csvColumn: 'Luft_Lage', dbPath: 'lueftung.Luft_Lage', certificateTypes: ['WG/B'] },
  { csvColumn: 'Luft_Typ', dbPath: 'lueftung.Luft_Typ', certificateTypes: ['WG/B'] },
];

// Helper function to get nested object value by path
export function getNestedValue(obj: any, path: string): any {
  if (!path) return undefined;
  
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// Helper function to transform enum values
export function transformEnumValue(fieldName: string, value: any): string {
  if (value === null || value === undefined) return '';
  
  const stringValue = String(value);
  const mapping = csvEnumMappings[fieldName];
  
  if (mapping && mapping[stringValue]) {
    return mapping[stringValue];
  }
  
  return stringValue;
}

// Helper function to format date values
export function formatDateValue(value: any): string {
  if (!value) return '';
  
  // If it's already in DD.MM.YYYY format, return as is
  if (typeof value === 'string' && /^\d{2}\.\d{2}\.\d{4}$/.test(value)) {
    return value;
  }
  
  // Try to parse as date and format
  try {
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}.${month}.${year}`;
    }
  } catch (error) {
    // If parsing fails, return original value
  }
  
  return String(value);
}

// Helper function to extract building component data
function extractBuildingComponentData(certificateData: EnergieausweisData): Record<string, any> {
  const componentData: Record<string, any> = {};

  if (certificateData.gebaeudedetails2) {
    // Process boeden (floors)
    if (certificateData.gebaeudedetails2.boeden && Array.isArray(certificateData.gebaeudedetails2.boeden)) {
      certificateData.gebaeudedetails2.boeden.forEach((boden: any, index: number) => {
        const prefix = `Boden${index + 1}`;
        componentData[prefix] = boden.bezeichnung;
        componentData[`${prefix}_massiv`] = boden.massiv;
        componentData[`${prefix}_Kellerdecke`] = boden.uebergang;
        componentData[`${prefix}_Fläche`] = boden.flaeche;
        componentData[`${prefix}_U-Wert`] = boden.uWert;
        componentData[`${prefix}_Dämmung`] = boden.daemmung;
      });
    }

    // Process daecher (roofs)
    if (certificateData.gebaeudedetails2.daecher && Array.isArray(certificateData.gebaeudedetails2.daecher)) {
      certificateData.gebaeudedetails2.daecher.forEach((dach: any, index: number) => {
        const prefix = `Dach${index + 1}`;
        componentData[prefix] = dach.bezeichnung;
        componentData[`${prefix}_massiv`] = dach.massiv;
        componentData[`${prefix}_Geschossdecke`] = dach.uebergang;
        componentData[`${prefix}_Fläche`] = dach.flaeche;
        componentData[`${prefix}_U-Wert`] = dach.uWert;
        componentData[`${prefix}_Dämmung`] = dach.daemmung;
      });
    }

    // Process waende (walls)
    if (certificateData.gebaeudedetails2.waende && Array.isArray(certificateData.gebaeudedetails2.waende)) {
      certificateData.gebaeudedetails2.waende.forEach((wand: any, index: number) => {
        const prefix = `Wand${index + 1}`;
        componentData[prefix] = wand.bezeichnung;
        componentData[`${prefix}_massiv`] = wand.massiv;
        componentData[`${prefix}_Fläche`] = wand.flaeche;
        componentData[`${prefix}_U-Wert`] = wand.uWert;
        componentData[`${prefix}_Dämmung`] = wand.daemmung;
      });
    }
  }

  // Process fenster (windows)
  if (certificateData.fenster && certificateData.fenster.fenster && Array.isArray(certificateData.fenster.fenster)) {
    certificateData.fenster.fenster.forEach((fenster: any, index: number) => {
      const prefix = `Fenster${index + 1}`;
      componentData[prefix] = fenster.bezeichnung;
      componentData[`${prefix}_Art`] = fenster.art;
      componentData[`${prefix}_Fläche`] = fenster.flaeche;
      componentData[`${prefix}_U-Wert`] = fenster.uWert;
      componentData[`${prefix}_Ausrichtung`] = fenster.ausrichtung;
      componentData[`${prefix}_Baujahr`] = fenster.baujahr;
    });
  }

  return componentData;
}

// Enhanced helper function to get nested object value by path with building component support
export function getNestedValueWithComponents(obj: any, path: string, componentData: Record<string, any>): any {
  if (!path) return undefined;

  // Check if this is a building component field
  if (componentData && (path.startsWith('Boden') || path.startsWith('Dach') || path.startsWith('Wand') || path.startsWith('Fenster'))) {
    return componentData[path];
  }

  return getNestedValue(obj, path);
}

// Main function to generate CSV data for a certificate
export function generateCsvData(certificateData: EnergieausweisData): string {
  const certificateType = certificateData.certificate_type as 'WG/V' | 'WG/B' | 'NWG/V';

  if (!certificateType) {
    throw new Error('Certificate type is required for CSV export');
  }

  // Extract building component data
  const componentData = extractBuildingComponentData(certificateData);

  // Filter mappings for the specific certificate type
  const relevantMappings = csvFieldMappings.filter(mapping =>
    mapping.certificateTypes.includes(certificateType)
  );

  // Generate header row
  const headers = relevantMappings.map(mapping => mapping.csvColumn);

  // Generate data row
  const dataRow = relevantMappings.map(mapping => {
    let value = getNestedValueWithComponents(certificateData, mapping.dbPath, componentData);

    // Use default value if no value found
    if (value === null || value === undefined) {
      value = mapping.defaultValue || '';
    }

    // Apply transformation if specified
    if (mapping.transform) {
      value = mapping.transform(value);
    } else {
      // Apply default transformations
      if (mapping.csvColumn.includes('_von') || mapping.csvColumn.includes('_bis')) {
        value = formatDateValue(value);
      } else {
        value = transformEnumValue(mapping.csvColumn, value);
      }
    }

    return String(value);
  });

  // Combine header and data with semicolon separator
  const csvContent = [headers.join(';'), dataRow.join(';')].join('\n');

  return csvContent;
}

// Function to generate filename for CSV export
export function generateCsvFilename(certificateData: EnergieausweisData): string {
  const orderNumber = certificateData.order_number || certificateData.id.slice(-8);
  const certificateType = certificateData.certificate_type || 'UNKNOWN';
  const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  
  return `EA-${orderNumber}-${certificateType}-${date}.csv`;
}

// Function to download CSV file
export function downloadCsvFile(csvContent: string, filename: string): void {
  // Create blob with UTF-8 encoding (without BOM as specified)
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // Create download link
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  URL.revokeObjectURL(url);
}
